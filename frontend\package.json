{"name": "frontend", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "lint": "eslint .", "preview": "vite preview"}, "dependencies": {"@clerk/clerk-react": "^5.37.0", "@sentry/react": "^10.1.0", "@stream-io/video-react-sdk": "^1.19.2", "@tailwindcss/vite": "^4.1.13", "@tanstack/react-query": "^5.83.0", "axios": "^1.11.0", "lucide-react": "^0.539.0", "react": "^19.1.1", "react-dom": "^19.1.1", "react-hot-toast": "^2.5.2", "react-router": "^7.6.3", "stream-chat": "^9.14.0", "stream-chat-react": "^13.3.0", "tailwindcss": "^4.1.11"}, "devDependencies": {"@eslint/js": "^9.32.0", "@types/react": "^19.1.9", "@types/react-dom": "^19.1.7", "@vitejs/plugin-react": "^4.7.0", "eslint": "^9.32.0", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.20", "globals": "^16.3.0", "vite": "^7.1.0"}}