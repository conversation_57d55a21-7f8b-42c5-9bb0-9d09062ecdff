.auth-container {
  min-height: 100vh;
  display: flex;
  background: linear-gradient(
      125deg,
      #1a0b2e 0%,
      #16213e 25%,
      #0f3460 50%,
      #533483 75%,
      #7209b7 100%
    ),
    radial-gradient(ellipse at top left, rgba(116, 58, 213, 0.15) 0%, transparent 50%),
    radial-gradient(ellipse at bottom right, rgba(74, 21, 75, 0.15) 0%, transparent 50%);
  position: relative;
  overflow: hidden;
}

.auth-left {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 3rem;
  position: relative;
}

.auth-right {
  flex: 0.8;
  position: relative;
  overflow: hidden;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 2rem;
}

.auth-hero {
  max-width: 500px;
  color: white;
  z-index: 2;
  padding: 3rem;
  background: rgba(255, 255, 255, 0.03);
  border-radius: 2rem;
  backdrop-filter: blur(25px);
  border: 1px solid rgba(116, 58, 213, 0.2);
  box-shadow: 0 30px 60px rgba(0, 0, 0, 0.15), 0 0 100px rgba(116, 58, 213, 0.1),
    inset 0 1px 0 rgba(255, 255, 255, 0.1);
  position: relative;
}

.brand-container {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.brand-logo {
  width: 3.5rem;
  height: 3.5rem;
  border-radius: 0.75rem;
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.brand-name {
  font-size: 2.5rem;
  font-weight: 700;
  font-family: "Monaco", monospace;
  letter-spacing: 0.1em;
  text-transform: uppercase;
  background: linear-gradient(45deg, #fff, #f0f0f0);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.hero-title {
  font-size: 3.5rem;
  font-weight: 800;
  line-height: 1.1;
  margin-bottom: 1rem;
  background: linear-gradient(45deg, #fff, #e0e0e0);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  margin-top: 2rem;
}

.hero-subtitle {
  font-size: 1.25rem;
  line-height: 1.6;
  margin-bottom: 2rem;
  opacity: 0.9;
  color: #f8f9fa;
}

.features-list {
  display: flex;
  flex-direction: column;
  gap: 1rem;
  margin-bottom: 2rem;
}

.feature-item {
  display: flex;
  align-items: center;
  gap: 1rem;
  padding: 1rem 1.25rem;
  background: rgba(116, 58, 213, 0.08);
  border-radius: 1rem;
  backdrop-filter: blur(20px);
  border: 1px solid rgba(116, 58, 213, 0.2);
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  box-shadow: 0 4px 20px rgba(74, 21, 75, 0.1), inset 0 1px 0 rgba(255, 255, 255, 0.05);
}

.feature-item:hover {
  background: rgba(116, 58, 213, 0.15);
  transform: translateX(0.75rem);
  box-shadow: 0 8px 30px rgba(74, 21, 75, 0.2), inset 0 1px 0 rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(116, 58, 213, 0.3);
}

.feature-icon {
  font-size: 1.5rem;
  width: 2.5rem;
  height: 2.5rem;
  display: flex;
  align-items: center;
  justify-content: center;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 50%;
}

.cta-button {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  background: linear-gradient(135deg, #7209b7 0%, #533483 50%, #4a154b 100%);
  color: white;
  font-size: 1.125rem;
  font-weight: 600;
  padding: 1.25rem 2.5rem;
  border-radius: 1rem;
  border: 1px solid rgba(114, 9, 183, 0.3);
  cursor: pointer;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  box-shadow: 0 20px 40px rgba(74, 21, 75, 0.3), 0 8px 20px rgba(114, 9, 183, 0.2),
    inset 0 1px 0 rgba(255, 255, 255, 0.1);
  position: relative;
  overflow: hidden;
  backdrop-filter: blur(15px);
}

.cta-button::before {
  content: "";
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(45deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: all 0.6s ease;
}

.cta-button:hover::before {
  left: 100%;
}

.cta-button:hover {
  transform: translateY(-2px);
  box-shadow: 0 25px 60px rgba(74, 21, 75, 0.4), 0 12px 30px rgba(114, 9, 183, 0.3),
    inset 0 2px 0 rgba(255, 255, 255, 0.15);
  border: 1px solid rgba(114, 9, 183, 0.4);
}

.button-arrow {
  transition: transform 0.3s ease;
}

.cta-button:hover .button-arrow {
  transform: translateX(0.25rem);
}

.auth-image-container {
  position: relative;
  max-width: 480px;
  max-height: 600px;
  width: 100%;
  border-radius: 2rem;
  overflow: hidden;
  box-shadow: 0 30px 60px rgba(74, 21, 75, 0.25), 0 15px 30px rgba(0, 0, 0, 0.15),
    inset 0 1px 0 rgba(255, 255, 255, 0.1);
  animation: float 6s ease-in-out infinite;
  border: 1px solid rgba(116, 58, 213, 0.2);
}

.auth-image {
  width: 100%;
  height: auto;
  object-fit: cover;
  object-position: center;
  display: block;
}

.image-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(45deg, rgba(74, 21, 75, 0.15), rgba(45, 27, 105, 0.15));
}

/* Responsive Design */
@media (max-width: 768px) {
  .auth-container {
    flex-direction: column;
  }

  .auth-left {
    padding: 2rem 1.5rem;
  }

  .auth-hero {
    padding: 2rem;
  }

  .auth-right {
    min-height: 40vh;
    padding: 1rem;
  }

  .auth-image-container {
    max-width: 360px;
    max-height: 420px;
    animation: float 5s ease-in-out infinite;
  }

  .hero-title {
    font-size: 2.5rem;
  }

  .brand-name {
    font-size: 2rem;
  }
}

.auth-container::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-image: radial-gradient(1px 1px at 25px 35px, rgba(116, 58, 213, 0.4), transparent),
    radial-gradient(1px 1px at 85px 75px, rgba(74, 21, 75, 0.3), transparent),
    radial-gradient(1px 1px at 145px 45px, rgba(114, 9, 183, 0.3), transparent),
    radial-gradient(1px 1px at 195px 85px, rgba(83, 52, 131, 0.3), transparent);
  background-repeat: repeat;
  background-size: 250px 120px;
  animation: gentleFloat 25s ease-in-out infinite;
  pointer-events: none;
  opacity: 0.6;
}

.auth-container::after {
  content: "";
  position: absolute;
  top: -25%;
  left: -25%;
  width: 150%;
  height: 150%;
  background: radial-gradient(circle, rgba(116, 58, 213, 0.03) 1px, transparent 1px);
  background-size: 80px 80px;
  animation: subtleDrift 40s linear infinite;
  pointer-events: none;
}

/* Floating Animation */
@keyframes float {
  0%,
  100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-20px);
  }
}

@keyframes gentleFloat {
  0%,
  100% {
    transform: translateY(0px) translateX(0px);
  }
  25% {
    transform: translateY(-15px) translateX(5px);
  }
  50% {
    transform: translateY(-8px) translateX(-3px);
  }
  75% {
    transform: translateY(-12px) translateX(2px);
  }
}

@keyframes subtleDrift {
  0% {
    transform: rotate(0deg) translateX(0px);
  }
  50% {
    transform: rotate(180deg) translateX(10px);
  }
  100% {
    transform: rotate(360deg) translateX(0px);
  }
}
