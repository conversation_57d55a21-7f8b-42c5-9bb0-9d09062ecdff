{"name": "backend", "version": "1.0.0", "main": "src/server.js", "scripts": {"dev": "nodemon --import ./instrument.mjs src/server.js", "start": "node --import ./instrument.mjs src/server.js"}, "keywords": [], "author": "", "type": "module", "license": "ISC", "description": "", "dependencies": {"@clerk/express": "^1.7.4", "@sentry/node": "^10.1.0", "cors": "^2.8.5", "cross-env": "^10.0.0", "dotenv": "^17.2.1", "express": "^5.1.0", "inngest": "^3.40.1", "mongoose": "^8.16.5", "stream-chat": "^8.60.0"}, "devDependencies": {"nodemon": "^3.1.10"}}